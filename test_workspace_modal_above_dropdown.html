<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workspace Modal Above Dropdown Test</title>
    <style>
        /* Simulate the modal structure */
        .sweet-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 120;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sweet-modal.theme-wide.theme-sticky-footer {
            background: white;
            border-radius: 8px;
            width: 50vw;
            height: 400px;
            position: relative;
            overflow: visible; /* Fixed: was hidden */
        }

        .sweet-content {
            padding: 20px;
            height: calc(100% - 4rem);
            overflow: visible; /* Fixed: was auto */
        }

        .box__inner {
            overflow: visible; /* Fixed: was hidden */
        }

        /* Simulate multiselect dropdown */
        .multiselect {
            position: relative;
            margin-top: 250px; /* Position near bottom to trigger "above" behavior */
        }

        .multiselect__tags {
            border: 1px solid #ccc;
            padding: 8px;
            background: white;
        }

        .multiselect__content-wrapper {
            position: absolute;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ccc;
            border-top: none;
            max-height: 200px;
            z-index: 121; /* Fixed: modal z-index + 1 */
        }

        /* Simulate multiselect--above behavior */
        .multiselect--above .multiselect__content-wrapper {
            bottom: 100%; /* Opens above the input */
            border-top: 1px solid #ccc;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            /* Fixed positioning for modal */
            position: absolute;
            z-index: 121;
            max-height: 200px !important;
            overflow-y: auto;
        }

        .multiselect__option {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }

        .multiselect__option:hover {
            background: #f0f0f0;
        }

        .test-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            z-index: 200;
        }

        .toggle-btn {
            margin: 10px 0;
            padding: 5px 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <strong>Workspace Modal Above Dropdown Fix Test</strong><br>
        The dropdown opens above and should be fully visible.<br>
        <button class="toggle-btn" onclick="toggleAbove()">Toggle Above/Below</button>
    </div>

    <div class="sweet-modal-overlay">
        <div class="sweet-modal theme-wide theme-sticky-footer">
            <div class="sweet-content">
                <div class="box__inner">
                    <h3>Workspace Edit - People Tab</h3>
                    <p>This dropdown is positioned near the bottom to trigger "above" behavior.</p>
                    
                    <div class="multiselect multiselect--above" id="dropdown">
                        <div class="multiselect__tags">
                            Select People... (Opens Above)
                        </div>
                        <div class="multiselect__content-wrapper">
                            <div class="multiselect__option">John Doe (<EMAIL>)</div>
                            <div class="multiselect__option">Jane Smith (<EMAIL>)</div>
                            <div class="multiselect__option">Bob Johnson (<EMAIL>)</div>
                            <div class="multiselect__option">Alice Brown (<EMAIL>)</div>
                            <div class="multiselect__option">Charlie Wilson (<EMAIL>)</div>
                            <div class="multiselect__option">David Lee (<EMAIL>)</div>
                            <div class="multiselect__option">Emma Davis (<EMAIL>)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleAbove() {
            const dropdown = document.getElementById('dropdown');
            const tags = dropdown.querySelector('.multiselect__tags');
            
            if (dropdown.classList.contains('multiselect--above')) {
                dropdown.classList.remove('multiselect--above');
                tags.textContent = 'Select People... (Opens Below)';
            } else {
                dropdown.classList.add('multiselect--above');
                tags.textContent = 'Select People... (Opens Above)';
            }
        }
    </script>
</body>
</html>
