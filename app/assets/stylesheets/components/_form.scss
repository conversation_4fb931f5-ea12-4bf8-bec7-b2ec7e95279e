$form-element-height: 38px;

.form-group {
  margin-bottom: 1.5rem;

  label {
    color: $themed-dark;
    font-size: 0.9375rem;
    font-weight: 500;
  }
}

.form-check .form-check-label {
  color: $themed-secondary;
  cursor: pointer;
  font-weight: 400;
  font-size: 100%;
}

.form-control {
  background-color: $themed-box-bg;
  color: $themed-base;
  font-size: 14px;

  &:focus {
    background-color: $themed-box-bg;
    color: $themed-base;
  }
}

.form-control,
.help {
  &.is_invalid {
    color: $red
  }
}

[disabled] {
  color: $themed-muted;
}

textarea::placeholder {
  color: $themed-very-fair;
}

input::placeholder {
  color: $themed-placeholder;
}

.form-control-lg {
  font-size: 1.15rem;
}

.form-box {
  border-bottom: 1px solid $themed-light;
  padding: 2rem 0;
}

.form-box--control-label {
  line-height: 0;
}

.type-selector {
  cursor: pointer;

  input {
    display: none;
    margin: 0;
    padding: 0;
  }

  input:active + .type-select {
    opacity: 0.9;
  }

  input:checked + .type-select {
    filter: none;
  }
}

$type-select-height: 2rem;

.type-select {
  transition: $transition-base;
  cursor: pointer;
  background-size: contain;
  background-repeat: no-repeat;
  display: inline-block;
  width: $type-select-height;
  height: $type-select-height;
}

.type-select-label {
  color: $themed-base;
  line-height: $type-select-height;
}

$switch-width: 150px;

.switch {
  position: relative;
  height: $form-element-height;
  width: $switch-width;
  background: $themed-light;
  border-radius: 3px;
}

.switch-label {
  position: relative;
  z-index: 2;
  float: left;
  width: $switch-width/2;
  line-height: $form-element-height;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
  text-transform: uppercase;
  color: $themed-muted;
}

.switch-label:active {
  font-weight: bold;
  color: $color-telecom;
}

.switch-input {
  display: none;
}

.switch-input:checked + .switch-label {
  font-weight: bold;
  color: white;
  transition: 0.15s ease-out;
}

.switch-input:checked + .switch-label-on ~ .switch-selection {
  left: $switch-width/2;
}

.switch-selection {
  display: block;
  position: absolute;
  z-index: 1;
  top: 2px;
  left: 2px;
  width: $switch-width/2 - 2;
  height: 34px;
  background: $color-telecom;
  border-radius: 3px;
  transition: left 0.15s ease-out;
}

.multiselect {
  color: $themed-base !important;
  min-height: $form-element-height !important;

  .multiselect__content-wrapper {
    background-color: $themed-box-bg;
    border-bottom-left-radius: $border-radius;
    border-bottom-right-radius: $border-radius;
    border-color: $themed-fair;
    box-shadow: $box-shadow;
    z-index: 1;
  }

  // Fix for multiselect dropdowns inside modals
  .sweet-modal & .multiselect__content-wrapper,
  .modal & .multiselect__content-wrapper,
  .tingle-modal & .multiselect__content-wrapper {
    z-index: map-get($zIndex, 'modal') + 1;
  }

  .multiselect__option--group,
  .multiselect__option--disabled {
    background-color: $themed-light !important;
  }

  .multiselect__input,
  .multiselect__single {
    background-color: $themed-box-bg;
    color: $themed-base;
  }

  .multiselect__input::placeholder {
    color: $themed-placeholder !important;
  }

  .multiselect__tags {
    background-color: $themed-box-bg;
    border-color: $themed-fair;
    border-radius: 0.5rem; // should match .form-control
    min-height: $form-element-height;
    position: relative;
    z-index: 2;
  }

  .multiselect__select {
    height: $form-element-height;
    z-index: 3;
  }

  .multiselect__select:before {
    // Using the same color as .form-control
    color: gray;
    border-color: gray transparent transparent;
  }

  .multiselect__option {
    font-size: 0.875rem;
    line-height: 1rem;
    min-height: 2.5rem;
    padding: 0.625rem 0.75rem;
  }

  .multiselect__option:after {
    font-size: 0.75rem;
    line-height: 2.25rem;
    padding: 0 0.75rem;
  }

  .multiselect__option--highlight {
    background: var(--themed-link);
  }

  .multiselect__option--highlight:after {
    background: var(--themed-link);
    color: rgba(255,255,255,0.75);
    padding-left: 0.125rem;
  }

  .multiselect__option--selected {
    background-color: $themed-lighter;

    &.multiselect__option--highlight, &.multiselect__option--highlight:after {
      background-color: $red-light;
    }
  }

  .multiselect__placeholder {
    color: $themed-placeholder;
    margin: 0;
    padding: 0;
  }

  &.multiselect--disabled {
    border-radius: $border-radius;
    
    .multiselect__select {
      background: $themed-light;
      border-radius: 0 $border-radius $border-radius 0;
      height: $form-element-height - 2px;
    }
  }

  &.multiselect--above {
    .multiselect__content-wrapper {
      border-radius: $border-radius $border-radius 0 0;
    }
  }
}

.multiselect__tags {
  .multiselect--active:not(.multiselect--above) & {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  .multiselect--active.multiselect--above & {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }
}

.transaction-tags {
  .multiselect__tag {
    background-color: theme-color-level('success', $alert-bg-level) !important;
    color: theme-color-level('success', $alert-color-level) !important;
  }

  .multiselect__tag-icon:after {
    color: $themed-secondary !important;
  }

  .multiselect__tag-icon:hover {
    background-color: $success !important;

    &:after {
      color: white !important;
    }
  }
}

.single-column-form-wrap {
  max-width: 40rem;
}

.double-column-form-wrap {
  max-width: 48rem;
}

.monthly-cost-input {
  max-width: 16rem;
}

.account-number-input {
  max-width: 25rem;
}

.btn-group-toggle {
  .btn {
    font-weight: normal;
  }

  .btn-outline-not-as-light:not(.active) {
    color: $themed-muted;

    &:hover {
      background-color: $themed-light;
      color: $themed-secondary;
    }
  }
}

.search-wrap {
  position: relative;
}

.search-input {
  padding-left: 2.25rem;
}

.search-input-icon {
  color: $themed-muted;
  font-size: 1.25rem;
  left: 0;
  position: absolute;
  text-align: center;
  top: 50%;
  transform: translateY(-50%);
  width: 2.25rem;
}

.like-input {
  border-radius: $border-radius;
  border: 1px solid $themed-very-fair;
  padding: 0.375rem 0.75rem;
  background-color: $themed-light;

  &[disabled] {
    background-color: $themed-light;
    color: $themed-base;
  }
}

.search-wrap {
  &.search-dropdown-open {
    .form-control {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    .search-dropdown-menu {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      display: block;
    }
  }
}

.search-dropdown-menu {
  display: none;
  height: auto;
  padding: 0;
  text-align: left;
  top: 3rem;
  width: 100%;
  z-index: 99;
}

.search-item {
  &:hover {
    background-color: $themed-light;
    cursor: pointer;
  }
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;utf8,<svg fill= 'grey' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position-x: 98%;
  background-position-y: 6px;
}

.required:after {
  content: " *";
  color: red;
}

.border-line {
  background-color: $color-fair;
  height: 1px;
}

.border-label {
  background: $themed-box-bg;
  border: 1px solid $color-fair;
  border-radius: $border-radius-lg;
  color: $themed-secondary;
  font-weight: 500;
  margin-right: 1rem;
  padding: 0.25rem 0.75rem;
  position: absolute;
  z-index: 1;

  &.border-label--small {
    padding: 0.125rem 0.5rem;
  }
}

.form-control-sm {
  border-radius: $border-radius;
}

select.form-control-sm {
  background-position-y: 4px;
}

.checkbox {
  background-color: $themed-box-bg;
  border-radius: 2px;
  border: 1px solid $themed-fair;
  color: $themed-box-bg;
  display: block;
  font-size: 0.75rem;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  transition: $transition-base;
  width: 1rem;
}

.checkbox-selected {
  background-color: $teal;
  border-color: $teal;
}

.checkbox-data {
  width: 0.875em;
}

.dropdown-toggle::after {
  margin-top: 0.15em;
}
