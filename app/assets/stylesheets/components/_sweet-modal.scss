// Scoped to body to override defaults with as little scoping as possible
body {
  .sweet-modal-overlay {
    background-color: rgba(var(--themed-overlay-bg-rgb-values), 0.425);
    max-height: none;
    overflow: auto;

    ::-webkit-scrollbar {
      width: 8px;
    }
  
    ::-webkit-scrollbar-track {
      background: transparent;
      border: none;
    }
  
    ::-webkit-scrollbar-thumb {
      background: var(--themed-fair);
      border-radius: 4px;
      transition: background 0.3s;
      opacity: 0.5;
    }
  
    ::-webkit-scrollbar-thumb:hover {
      background: var(--themed-very-muted);
    }
  
    ::-webkit-scrollbar-thumb:active {
      background: var(--themed-very-muted);
    }
  }

  .sweet-modal {
    background: $themed-box-bg;
    border-radius: $border-radius-lg;
    box-shadow: map-get($shadows, 'top');
    height: auto;
    margin-bottom: 3rem;
    max-height: none;
    transform: translateX(-50%) scale(1);
    top: 3rem;
    overflow: auto;

    &.is-visible {
      transform: translateX(-50%);
    }

    .sweet-box-actions {
      align-items: center;
      display: flex;
      flex-direction: row;
      z-index: 99;

      .sweet-action-close {
        color: $themed-base;
      }
    }

    // Right side drawer sweet-modal useage on the component with optional full-height
    // modal-theme="right"
    &.theme-right {
      border-radius: $border-radius 0 0 $border-radius;
      bottom: 0;
      height: 100vh;
      margin-bottom: 0;
      max-height: 100vh;
      overflow: scroll;
      left: unset;
      right: 0;
      top: 0;
      transform: translate3d(100%,0,0);
      width: 40vw;

      &.is-visible {
        left: unset;
        transform: translate3d(0,0,0);
      }

      .sweet-title {
        border-top-left-radius: 0.5rem;
      }
    }

    &.theme-center {
      border-radius: $border-radius 0 0 $border-radius;
      bottom: 0;
      height: calc(100vh - 3rem); 
      margin-bottom: 0;
      overflow: auto;
      top: 3rem;
      width: 40vw;
      .sweet-title {
        border-top-left-radius: 0.5rem;
      }
    }

    &.theme-more-info {
      max-width: 32rem;
    }
    
    &.theme-wide {
      max-width: unset;
      width: 50vw;
    }

    &.theme-full {
      max-width: unset;
      width: 90vw;
    }

    &.theme-dark-header {
      .sweet-title {
        background-color: var(--themed-dark-drawer-bg);
        color: #fff !important;
      }
    
      .sweet-action-close {
        color: #fff !important;
      }
    }

    &.theme-centered-title {
      .sweet-title {
        padding-left: 0.125rem !important;
        padding-right: 0.125rem !important;
        text-align: center !important;
      }
    }

    &.theme-sticky-footer {
      $sweet_title_height: 4rem;
      $sweet_content__default-padding: 4rem;
      $sweet-modal__default-padding: 2rem;

      overflow: hidden;

      .sweet-content {
        height: calc(100% - #{$sweet_title_height + $sweet_content__default-padding});
        overflow: auto;
      }

      .sweet-content-content {
        height: 100%;
      }

      .sweet-modal__sticky-footer {
        background: var(--themed-box-bg);
        bottom: 0;
        border-radius: 0 0 0 0.5rem;
        border-top: 1px solid rgba(var(--themed-base-rgb-values), 0.1);
        margin-left: -$sweet-modal__default-padding;
        margin-bottom: 0 !important;
        padding: 0.5rem;
        position: absolute;
        width: 100%;
      }
    }
  }
  
  // Improper use of theming, forcing us to do classList changes instead of regular sweet modal theming
  // TODO: Set .theme-full to be a peer class of sweet-modal, and update its usage throughout vue accordingly
  //       Alternatively, use .theme-full as an overlay theme
  .theme-full .sweet-modal {
    max-width: unset;
    width: 90vw;
  }

  .tags-modal > .sweet-modal,
  .warranty-acquisition-modal > .sweet-modal,
  .transactions-modal > .sweet-modal,
  .contract-hierarchy-modal > .sweet-modal {
    overflow: visible;
  }

  // Fix for workspace edit modal dropdown visibility
  .workspace-edit-modal > .sweet-modal {
    overflow: visible;

    .sweet-content {
      overflow: visible;
    }
  }

  .bounce {
    animation-name: none !important;
  }

  .sweet-modal-overlay {
    z-index: map-get($zIndex, 'modal');
  }
}

.sweet-custom-footer {
  margin: 0 -2rem;
}
