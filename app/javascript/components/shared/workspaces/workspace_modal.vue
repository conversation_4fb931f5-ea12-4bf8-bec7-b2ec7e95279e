<template>
  <sweet-modal
    ref="workspaceModal"
    v-sweet-esc
    blocking
    :modal-theme="'wide theme-sticky-footer'"
    :title="workspaceTitle"
    :data-tc-title="`workspaceTitle`"
    @close="handleCancel"
  >
    <div v-if="loading || !object && false">
      <pulse-loader
        color="#0d6efd"
        size="0.5rem"
        :loading="true"
      />
    </div>
    <div v-else-if="object">
      <div class="subpage-menu mt-1 mb-4 d-inline-block">
        <template v-if="showWorkspaceTabs">
          <div
            v-for="page in pages"
            :key="page.name"
            :class="['clickable subpage-menu__item', isActiveClass(page.name)]"
            :data-tc-subpage-menu-item="page.name"
            @click="setViewPage(page)"
          >
            {{ page.label }}
          </div>
        </template>
      </div>

      <div>
        <div
          v-if="object"
          class="box__inner box--height box__inner box--height bg-themed-modal-sticky-footer-Nope p-3-Nope rounded pb-2"
        >
          <div v-if="isInfoPage">
            <form>
              <div class="row">
                <div class="col offset-0">
                  <div class="form-group mb-3">
                    <label class="required">Basic Info</label>
                    <input
                      v-model="object.name"
                      type="text"
                      class="form-control bg-transparent border-right-0 border-left-0 border-top-0 rounded-0 shadow-none form-control-lg px-0 template-name--input big"
                      placeholder="Workspace name"
                      required
                      data-tc-field="workspace name"
                      @input="validateWorkspaceName(object)"
                    >
                    <span
                      v-if="workspaceNameError"
                      class="form-text small text-danger"
                      data-tc-error="workspace name"
                    >
                      {{ workspaceNameError }}
                    </span>
                  </div>
                  <div class="form-group mb-3">
                    <input
                      v-model="object.description"
                      type="text"
                      class="form-control bg-transparent border-right-0 border-left-0 border-top-0 rounded-0 shadow-none form-control-lg px-0 template-name--input not-as-small"
                      placeholder="A basic description of your workspace"
                      data-tc-field="workspace description"
                    >
                  </div>
                </div>
                <div class="form-group col-2 offset-1">
                  <div data-tc-icon="workspace icon">
                    <label class="d-block mb-2 ml-n2">Icon</label>
                    <style-select-dropdown
                      class="ml-n2"
                      :object="iconOptions"
                      :value="{class:`${object.iconClass || 'genuicon-workspace'}`}"
                      :color-class="`${object.colorClass || 'primary'}`"
                      @input="changeWorkspaceIcon"
                    />
                  </div>
                </div>
                <div class="form-group col-2">
                  <div data-tc-icon="workspace color">
                    <label class="d-block mb-2 ml-n2">Color</label>
                    <style-select-dropdown
                      class="ml-n1"
                      :object="[
                        {class: 'dot bg-fair'},
                        {class: 'dot bg-secondary'},
                        {class: 'dot bg-gray'},
                        {class: 'dot bg-dark'},
                        {class: 'dot bg-primary'},
                        {class: 'dot bg-warning'},
                        {class: 'dot bg-success'},
                        {class: 'dot bg-biscay-blue'},
                        {class: 'dot bg-blue-oblivion'},
                        {class: 'dot bg-azure-blue'},
                        {class: 'dot bg-safe'},
                        {class: 'dot bg-purple'},
                        {class: 'dot bg-indigo'},
                        {class: 'dot bg-blue-gray'},
                      ]"
                      :value="{class:`dot bg-${object.colorClass || 'primary'} m-0`}"
                      :color-class="`${object.colorClass || 'primary'}`"
                      data-tc-colors="workspace"
                      @input="changeWorkspaceColor"
                    />
                  </div>
                </div>
              </div>
              <div
                v-if="object.id"
                class="form-group bg-themed-box-bg rounded p-3-Nope mt-4 mb-2"
              >
                <label>Open Ticket Portal</label>
                <div>
                  <a
                    :href="workspaceUrl"
                    target="_blank"
                  >
                    {{ workspaceUrl }}
                  </a>
                </div>
              </div>
              <div class="form-group bg-themed-box-bg rounded p-3-Nope mt-4 mb-2">
                <label class="required">Custom Forms</label>
                <div class="text-muted small mb-3">
                  Select existing custom forms you want copied to get you started with the new workspace.
                </div>
                <div class="row">
                  <div class="col position-relative">
                    <form-assign
                      v-model="object.customForms"
                      :is-default-present="isDefaultPresent()"
                      @input="validateCustomForms"
                    />
                    <span
                      v-if="customFormsError"
                      class="form-text small text-danger position-absolute ml-1"
                      style="top: 50px;"
                    >
                      {{ customFormsError }}
                    </span>
                  </div>
                </div>
              </div>
              <div
                v-if="object.id"
                class="form-group bg-themed-box-bg rounded p-3-Nope mt-4 mb-6 my-5"
              >
                <div>Default Workspace</div>
                <div class="text-muted small mb-4 row align-items-start">
                  <span class="col-8">
                    Your default workspace will be set as your active workspace on new logins and page refreshes.
                  </span>
                  <material-toggle
                    v-if="true"
                    ref="defaultWorkspaceToggle"
                    class="col-4 text-right"
                    :init-active="object.default"
                    @toggle-sample="updateDefaultWorkspace"
                  />
                </div>
              </div>
            </form>

          </div>
          <div v-else-if="isPeoplePage">
            <form>
              <div class="form-group mt-0">
                <label>Members</label>
                <div class="text-muted small mb-3">
                  These people and groups will have immediate access to the new workspace.
                </div>
                <div class="row">
                  <div class="col mb-4">
                    <mass-assign
                      v-if="object.permissions"
                      v-model="object.permissions"
                      @input="validatePermissions"
                    />
                  </div>
                </div>
                <div class="text-danger small mb-3">
                  {{ permissionsError }}
                </div>
              </div>
            </form>
          </div>
          <div v-else-if="isQuickSettingsPage">
            <form>
              <div class="form-group mt-0">
                <label data-tc-label="quick setting">Quick Settings</label>
                <div class="text-muted small mb-3">
                  Customize some common settings now while the workspace is created.
                </div>
              </div>

              <table class="table bg-themed-box-bg rounded mb-5">
                <tbody>
                  <template v-for="(section, index) in sections">
                    <tr
                      v-for="setting in object.quickSettings[section]"
                      :key="setting.id"
                      class="position-relative"
                      :data-tc="toTitle(setting.title)"
                    >
                      <td>
                        <p class="not-as-small font-weight-bold mb-1">
                          {{ toTitle(setting.title) }}
                        </p>
                        <p class="text-secondary small mb-0 p--responsive">
                          {{ setting.description }}
                        </p>
                        <div
                          v-if="index === 1"
                          class="d-flex"
                        >
                          <span class="mb-4 col-6 px-0 mr-2 mt-2">
                            Prefix
                            <input
                              v-model="setting.options.customPrefix"
                              :disabled="!enabled(setting)"
                              :class="{ 'is-invalid': customPrefixError }"
                              type="text"
                              name="custom prefix"
                              data-tc-input="prefix"
                              class="d-inline-block form-control mb-1"
                              aria-required="true"
                              aria-invalid="false"
                              @input="validateCustomPrefix(setting, section)"
                            >
                            <span 
                              v-if="customPrefixError" 
                              class="text-danger smallest"
                            >
                              {{ customPrefixError }}
                            </span>
                          </span>
                          <span class="mb-4 col-6 px-0 mt-2">
                            Initial Ticket Number
                            <input
                              v-model="setting.options.customTicketNumber"
                              :disabled="!enabled(setting)"
                              :class="{ 'is-invalid': customTicketNumberError }"
                              type="number"
                              min="1"
                              name="custom ticket number"
                              data-tc-input="ticket number"
                              class="d-inline-block form-control mb-1"
                              aria-required="true"
                              aria-invalid="false"
                              @input="validateTicketNumber(setting, section)"
                            >
                            <span 
                              v-if="customTicketNumberError" 
                              class="text-danger smallest"
                            >
                              {{ customTicketNumberError }}
                            </span>
                          </span>
                        </div>
                      </td>
                      <td class="text-right border-0">
                        <material-toggle
                          class="mt-1"
                          :init-active="enabled(setting)"
                          @toggle-sample="update(setting, section)"
                        />
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </form>
          </div>
        </div>
        <div class="sweet-modal__sticky-footer text-right pr-3 py-3">
          <button
            v-if="showPreviousButton"
            class="btn btn-sm btn-link text-secondary mr-2 float-left ml-3"
            @click="gotoPreviousPage()"
          >
            <span class="nulodgicon-arrow-left-c mr-1" />
            {{ previousPageText }}
          </button>
          <submit-button
            :btn-content="isNewRoute ? 'Create Workspace' : 'Update Workspace'"
            :saving-content="savingContent"
            :disabled="isSubmitDisabled"
            :is-validated="hasSeenAllPages"
            :is-saving="isSubmitting"
            :btn-classes="'px-4 form-create-btn'"
            @submit="createOrEditWorkspace()"
          />
          <button
            class="btn btn-link text-secondary px-4 form-create-btn ml-2"
            @click="gotoNextPage()"
          >
            {{ nextPageText }}
            <span
              v-if="!isQuickSettingsPage"
              class="nulodgicon-arrow-right-c ml-1" 
            />
          </button>
        </div>
      </div>
    </div>
  </sweet-modal>
</template>

<script>
  import http from 'common/http';
  import { mapMutations, mapActions, mapGetters } from 'vuex';
  import permissionsHelper from 'mixins/permissions_helper';
  import validateWorkspaceName from 'mixins/help_ticket_workspace_helper';
  import strings from 'mixins/string';
  import Articles from "mixins/articles";
  import { SweetModal } from 'sweet-modal-vue';
  import _cloneDeep from 'lodash/cloneDeep';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import SubmitButton from 'components/shared/submit_button.vue';
  import StyleSelectDropdown from 'components/shared/style_select_dropdown.vue';
  import MaterialToggle from 'components/shared/material_toggle.vue';
  import FormAssign from "./form_assign.vue";
  import MassAssign from "./mass_assign.vue";

  export default {
    components: {
      SweetModal,
      PulseLoader,
      FormAssign,
      StyleSelectDropdown,
      MaterialToggle,
      MassAssign,
      SubmitButton,
    },
    mixins: [permissionsHelper, Articles, strings, validateWorkspaceName],
    props: {
      workspace: {
        type: Object,
        default: () => null,
      },
    },
    data() {
      return {
        object: _cloneDeep(this.workspace),
        viewPage: { name: 'info', label: 'Info' },
        customFormsError: null,
        isSubmitting: false,
        shouldSetAsDefault: null,
        pages: [
          { name: 'info', label: 'Info', hasSeen: true },
          { name: 'people', label: 'People', hasSeen: false },
          { name: 'quick_settings', label: 'Quick Settings', hasSeen: false },
        ],
        iconOptions: [
          {class: 'genuicon-workspace'},
          {class: 'genuicon-accounting'},
          {class: 'genuicon-city'},
          {class: 'genuicon-classroom'},
          {class: 'genuicon-computer'},
          {class: 'genuicon-customer-service'},
          {class: 'genuicon-data'},
          {class: 'genuicon-delivery'},
          {class: 'genuicon-factory'},
          {class: 'genuicon-farm'},
          {class: 'genuicon-hr'},
          {class: 'genuicon-it'},
          {class: 'genuicon-paperwork'},
          {class: 'genuicon-location-thin'},
          {class: 'genuicon-office'},
          {class: 'genuicon-remote-office'},
          {class: 'genuicon-barcode'},
          {class: 'genuicon-keyboard'},
          {class: 'genuicon-star'},
          {class: 'genuicon-nav-vendor-spend'},
        ],
        permissionsError: null,
        workspaceNameError: null,
        customPrefixError: null,
        customTicketNumberError: null,
        loading: true,
        defaultWorkspaceId: null,
        sections: ['emailSettings', 'displaySettings', 'accessSettings'],
        addCustomForm: false,
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['currentCompanyUser']),
      isInfoPage() {
        return this.viewPage.name === 'info';
      },
      workspaceTitle() {
        if (this.isNewRoute) {
          return 'New Workspace';
        }
        return 'Edit Workspace';
      },
      savingContent() {
        if (this.object.id) {
          return 'Update Workspace';
        }
        return 'Create Workspace';
      },
      isPeoplePage() {
        return this.viewPage.name === 'people';
      },
      isQuickSettingsPage() {
        return this.viewPage.name === 'quick_settings';
      },
      previousPageText() {
        if (this.isPeoplePage) {
          return "Back to Info";
        } else if (this.isQuickSettingsPage) {
          return "Back to People";
        }

        return "";
      },
      nextPageText() {
        if (this.isInfoPage) {
          return "People";
        } else if (this.isPeoplePage) {
          return "Quick Settings";
        }

        return "";
      },
      hasSeenAllPages() {
        return this.pages.filter(page => page.hasSeen).length === this.pages.length && !this.isSubmitDisabled;
      },
      showWorkspaceTabs() {
        return this.isWriteAny || this.isReadAny;
      },
      isEditRoute() {
        return this.$route.query.edit;
      },
      isNewRoute() {
        return this.$route.query.new;
      },
      workspaceId() {
        return this.$route.params.id;
      },
      isDefaultWorkspace() {
        return this.defaultWorkspaceId  === this.object.id;
      },
      workspaceUrl() {
        const loc = document.location;
        return `${loc.protocol}//${loc.host}/help_center/workspaces/${this.object.id}`;
      },
      isSubmitDisabled() {
        const nameMissing = !this.object.name;
        const invalidCustomForms =
          !this.object.customForms?.length ||
          !this.object.customForms[0]?.id;

        return this.isNewRoute && (nameMissing || invalidCustomForms);
      },
      showPreviousButton() {
        return this.previousPageText && !this.isEditRoute && !this.isSubmitting;
      },
    },
    watch: {
      workspace: {
        handler() {
          this.object = _cloneDeep(this.workspace);
        },
        deep: true,
      },
    },
    methods: {
      ...mapMutations(['setLoadingStatus']),
      ...mapMutations('GlobalStore', ['setCurrentCompanyUser', 'setWorkspaceOptions']),
      ...mapActions(['fetchArticles']),
      onWorkspaceChange() {
        this.setLoadingStatus(false);
        if (this.isNewRoute && !this.$refs.workspaceModal.visible) {
          this.initWorkspace();
        }
        return this.currentUserDefaultWorkspace();
      },
      createOrEditWorkspace() {
        return this.isNewRoute ? this.createWorkspace() : this.saveWorkspace();
      },
      enabled(setting) {
        return setting.enabled;
      },
      update(setting, sectionName) {
        const selectedSetting = this.object.quickSettings[sectionName].filter((id) => id.id === setting.id)[0];
        selectedSetting.enabled = !selectedSetting.enabled;
      },
      isDefaultPresent() {
        if (this.object.customForms) {
          return Object.values(this.object.customForms).some(form => form.default === true);
        }
        return false;
      },
      updateDefaultWorkspace(value) {
        this.object.default = !this.object.default; 
        this.shouldSetAsDefault = value;
      },
      fetchQuickSettings() {
        http
          .get(`/workspace_quick_settings.json`)
          .then((res) => {
            this.object.quickSettings = res.data.quickSettings;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error loading workspace quick settings.');
          });
      },
      handleCancel() {
        this.reset();
        this.close();
      },
      reset() {
        this.viewPage = { name: 'info', label: 'Info' };
        this.addCustomForm = false;
        this.customFormsError = null;
        this.permissionsError = null;
        this.workspaceNameError = null;
        this.pages = [
          { name: 'info', label: 'Info', hasSeen: true },
          { name: 'people', label: 'People', hasSeen: false },
          { name: 'quick_settings', label: 'Quick Settings', hasSeen: false },
        ];
        this.object = _cloneDeep(this.workspace);
      },
      isActiveClass(page) {
        return { 'router-link-exact-active': this.viewPage.name === page };
      },
      setViewPage(page) {
        if (this.isInfoPage) {
          this.validateWorkspaceName(this.object);
          this.addCustomForm = true;
          this.validateCustomForms();
          if (this.workspaceNameError || this.customFormsError) {
            return;
          }
        }
        this.viewPage = page;
        this.pages.find(p => p.name === page.name).hasSeen = true;
      },
      gotoPreviousPage() {
        if (this.isPeoplePage) {
          this.setViewPage(this.pages[0]);
        } else if (this.isQuickSettingsPage) {
          this.setViewPage(this.pages[1]);
        }
      },
      gotoNextPage() {
        if (this.isInfoPage) {
          this.setViewPage(this.pages[1]);
        } else if (this.isPeoplePage) {
          this.setViewPage(this.pages[2]);
        }
      },
      open() {
        if (!this.$refs.workspaceModal.visible) {
          if (this.isEditRoute) {
            const workspaceId = this.$route.query.edit;
            this.loading = true;
            this.fetchWorkspace(workspaceId);
          } else {
            this.loading = true;
            this.initWorkspace();
          }
          this.$refs.workspaceModal.open();
        }
      },
      fetchWorkspace(workspaceId) {
        http
          .get(`/workspaces/${workspaceId}.json`)
          .then((res) => {
            this.object = res.data;
            this.loading = false;
            this.validateWorkspaceName(res.data);
          })
          .catch(() => {
            this.loading = false;
            this.emitError('Sorry, there was an error loading workspaces.');
          });
      },
      close() {
        if (this.$refs.workspaceModal.visible) {
          this.$refs.workspaceModal.close();
        }
        if (this.isNewRoute || this.isEditRoute) {
          this.$router.replace('/workspaces');
        }
      },
      initWorkspace() {
        http
          .get(`/group_options.json`)
          .then((res) => {
            const workspace = {
              name: null,
              permissions: [],
              customForms: [],
              iconClass: 'genuicon-workspace',
              colorClass: 'primary',
              quickSettings: [],
            };
            res.data.groups.forEach((g) =>  {
              if (g.name === 'Everyone') {
                workspace.permissions.push({ contributor: { id: g.contributorId, name: g.name }, permission: 'basicread' });
              } else if (g.name === 'Admins') {
                workspace.permissions.push({ contributor: { id: g.contributorId, name: g.name }, permission: 'write' });
              }
            });
            this.object = workspace;
            this.fetchQuickSettings();
            this.loading = false;
          })
          .catch((error) => {
            this.loading = false;
            this.emitError(`Sorry, there was an error loading workspaces. ${error}`);
          });
      },
      validatePermissions() {
        if (!this.object.permissions || !this.object.permissions[0].contributor) {
          this.permissionsError = "Must have one member";
        } else {
          this.permissionsErrors = null;
        }
      },
      createWorkspace() {
        this.isSubmitting = true;
        if (this.validateAndSetErrors()) {
          this.isSubmitting = false;
          return;
        }
        this.object.customForms = this.object.customForms.filter(cf => cf.id != null);
        this.$router.replace('/workspaces');
        http
          .post(`/workspaces.json`, { workspace: this.object })
          .then(() => {
            // eslint-disable-next-line no-undef
            updatePermissions();
            this.isSubmitting = false;
            this.close();
            this.emitSuccess(`Successfully saved workspace`);
            this.$emit('workspace-created');
            this.fetchWorkspaceOptions();
          })
          .catch((error) => {
            this.isSubmitting = false;
            this.emitError(`Sorry, there was an error saving this workspace. ${error.response.data}`);
          });
      },
      fetchWorkspaceOptions() {
        const company = getCompanyFromStorage();
        const url = `/workspace_options.json?company_id=${company.id}`;
        http
          .get(url, { params: { privilege_name: 'HelpTicket' } })
          .then((res) => {
            this.setWorkspaceOptions(res.data);
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading workspace options.`);
          });
      },
      saveWorkspace() {
        this.isSubmitting = true;
        if (this.shouldSetAsDefault !== null) {
          this.setUnsetDefaultWorkspace();
        }
        if (this.validateAndSetErrors()) {
          this.isSubmitting = false;
          return;
        }
        this.object.customForms = this.object.customForms.filter(cf => cf.id != null);
        this.$router.replace('/workspaces');
        http
          .put(`/workspaces/${this.object.id}.json`, { workspace: this.object })
          .then((res) => {
            this.isSubmitting = false;
            this.close();
            this.updateFilterName(res.data);
            this.emitSuccess(`Successfully saved workspace`);
          })
          .catch((error) => {
            this.isSubmitting = false;
            this.emitError(`Sorry, there was an error saving this workspace. ${error.response?.data}`);
          });
      },
      updateFilterName(workspace) {
        const { filtersData } = this.$store.state;
        const workspaceFilter = filtersData.find(filter =>
          filter.filter === 'setWorkspaceFilter' && filter.item.id === workspace.id
        );
        if (workspaceFilter) {
          const filterData = {
            filter: 'setWorkspaceFilter',
            item: {
              ...workspaceFilter.item,
              name: workspace.name,
            },
          };
          this.$store.commit('setFiltersData', filterData);
          this.$store.commit(filterData.filter, filterData.item);
        }
      },
      validateCustomPrefix(setting, sectionName) {
        const selectedSetting = this.object.quickSettings[sectionName].filter((id) => id.id === setting.id)[0];
        if (!selectedSetting.options.customPrefix) {
        this.customPrefixError = 'This field is required';
        } else if (!/^[a-zA-Z]+$/.test(selectedSetting.options.customPrefix)) {
          this.customPrefixError = 'This field must contain only alphabetic characters';
        } else {
          this.customPrefixError =  null;
        }
      },
      validateTicketNumber(setting, sectionName) {
        const selectedSetting = this.object.quickSettings[sectionName].filter((id) => id.id === setting.id)[0];
        if (!selectedSetting.options.customTicketNumber) {
        this.customTicketNumberError = 'This field is required';
        } else if (!/^\d+$/.test(selectedSetting.options.customTicketNumber)) {
          this.customTicketNumberError = 'This field must contain only integer numbers';
        } else {
          this.customTicketNumberError =  null;
        }
      },
      validateCustomForms() {
        const hasValidForm = this.object.customForms?.length && this.object.customForms[0]?.id;
        this.customFormsError = !hasValidForm && this.addCustomForm
          ? 'Must have one custom form selected'
          : null;
      },
      changeWorkspaceIcon(iconStyle) {
        this.$set(this.object, 'iconClass', iconStyle.class);
      },
      changeWorkspaceColor(color) {
        const match = color.class.match(/bg-([^ ]+)/);
        const colorClass = match ? match[1] : '';
        this.$set(this.object, 'colorClass', colorClass);
      },
      setUnsetDefaultWorkspace() {
        if (this.shouldSetAsDefault === null || this.shouldSetAsDefault === this.isDefaultWorkspace) {
          return;
        }
        const url = this.shouldSetAsDefault ? `/set_default_workspace.json?workspace={"id":${this.object.id}}` : '/unset_default_workspace.json';
        http
          .put(url)
          .catch((error) => {
            this.emitError(`Sorry, there was an error setting default workspace. ${error.response.data}`);
          });
      },
      validateAndSetErrors() {
        this.addCustomForm = true;
        this.validateWorkspaceName(this.object);
        this.validateCustomForms();
        let errorMessage = '';
        if (this.customPrefixError) {
          errorMessage = errorMessage.concat(`${this.customPrefixError}. `);
        }
        if (this.customTicketNumberError) {
          errorMessage = errorMessage.concat(`${this.customTicketNumberError}. `);
        }
        if (this.workspaceNameError) {
          errorMessage = errorMessage.concat(`${this.workspaceNameError}. `);
        }
        if (this.permissionsError) {
          errorMessage = errorMessage.concat(`${this.permissionsError}.`);
        }
        if (this.customFormsError) {
          errorMessage = errorMessage.concat(` ${this.customFormsError}.`);
        }
        if (errorMessage) {
          this.emitError(`${errorMessage} Please fix errors before saving.`);
          this.setViewPage(this.pages[0]);
          return true;
        }
        return false;
      },
      currentUserDefaultWorkspace() {
        if (this.currentCompanyUser) {
          this.defaultWorkspaceId = this.currentCompanyUser.defaultWorkspaceId;
          return;
        }
        if (!this.$superAdminUser) {
          const params = { privilege_name: this.moduleName };
          http
            .get(`/company_users/${Vue.prototype.$currentCompanyUserId}.json`, { params })
            .then((res) => {
              this.setCurrentCompanyUser(res.data);
              this.defaultWorkspaceId = res.data.defaultWorkspaceId;
            })
            .catch(() => {
              this.emitError('Sorry, there was an error getting user default workspace.');
            });
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  :deep(.sweet-modal) {
    .sweet-title {
      align-items: center;
      display: flex;
      height: unset;
      min-height: 4rem;
      padding-right: 9.5rem;

      h2 {
        font-size: 1.125rem;
        line-height: 1.25rem;
        overflow: unset;
        white-space: normal;
      }
    }

    .sweet-box-actions {
      display: flex;
    }

    .sweet-content {
      padding-top: 0.5rem;
    }
  }

  .article-box {
    display: block;
    min-height: 15rem;
    max-height: 35rem;
  }

  .box--height {
    min-height: 15rem;
  }

  .fullscreen-icon {
    position: relative;
    top: 0.125rem;
  }

  :deep(.sweet-modal) {
    overflow: visible;

    .sweet-content {
      overflow: visible;
    }

    .multiselect__content-wrapper {
      z-index: 121;
    }
  }
</style>
