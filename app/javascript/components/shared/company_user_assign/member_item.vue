<template>
  <div>
    <div
      v-if="!member.id"
      class="name-display"
    >
      <div class="pl-0 d-table-cell align-middle">
        <contributors-select
          v-if="isWriteAny"
          :value="member"
          :only-members="onlyMembers"
          :disabled="disabled"
          :excludes="excludeIds"
          :truncate-text="truncateText"
          :add-new-user="addNewUser"
          :automated-task-assign-value="automatedTaskAssignValue"
          :custom-form-at-assign-value="customFormAtAssignValue"
          :inside-modal="true"
          compact
          @select="changeMember(index, ...arguments)"
          @remove="clearMember(index)"
        />
      </div>
      <div
        v-show="remove"
        class="float-right remove-company-user text-muted table-cell text-right mr-2"
        style="line-height: 38px; width: 2rem;"
        data-tc-remove-icon="member dropdown"
        @click="removeMember(index)"
      >
        &times;
      </div>
    </div>
    <div
      v-else
      class="name-display name-hoverable py-1"
    >
      <div 
        class="pl-0 d-table-cell align-middle"
        :class="{ 'opacity-50': !member.active && (automatedTaskAssignValue || customFormAtAssignValue), 'text-muted': !member.active && (automatedTaskAssignValue || customFormAtAssignValue) }"
      >
        <span
          v-tooltip="member.email"
          class="float-left align-middle"
          style="width: 38px; max-width: 38px;"
        >
          <user-avatar
            :username="name"
            :src="userAvatar"
            :size="30"
            class="logo-outline"
          />
        </span>
        <span
          class="float-left mt-1 ml-1 avatar-name"
          :data-tc-member="name"
        >
          {{ name }}
        </span>
        <span
          v-if="!member.active && (automatedTaskAssignValue || customFormAtAssignValue)" 
          class="float-left mt-1 ml-1"
        >(Deactivated)</span>
      </div>
      <div
        v-if="isWriteAny"
        v-show="!disabled"
        class="float-right remove-company-user text-muted table-cell text-right mr-2"
        style="width: 2rem;"
        data-tc-remove-icon="staff user"
        :data-tc-remove="name"
        :data-tc-disable-remove="disabled"
        @click="removeMember(index)"
      >
        &times;
      </div>
    </div>
  </div>
</template>

<script>
import { Avatar as UserAvatar } from 'vue-avatar';
import permissionsHelper from 'mixins/permissions_helper';
import ContributorsSelect from '../contributors_select.vue';

export default {
  components: {
    ContributorsSelect,
    UserAvatar,
  },
  mixins: [ permissionsHelper ],
  props: ['member', 'options', 'index', 'remove', 'disabled', 'excludes', 'onlyMembers', 'truncateText', 'addNewUser', 'automatedTaskAssignValue', 'customFormAtAssignValue'],
  computed: {
    name() {
      return this.member.name || this.member.fullName || this.member.email || '';
    },
    excludeIds() {
      // You can't exclude the current member, so we have to add it back in
      const ids = [...this.excludes];
      const idx = ids.indexOf(this.member.id);
      if (idx > -1) {
        ids.splice(idx, 1);
      }
      return ids;
    },
    userAvatar() {
      return this.member.avatar || this.member.avatarThumbUrl;
    },
  },
  methods: {
    removeMember(index) {
      this.$emit("remove", index);
    },
    changeMember(...args) {
      this.$emit("input", ...args);
    },
    clearMember(index) {
      this.$emit("clear", index);
    },
  },
};
</script>

<style lang="scss" scoped>
.remove-company-user {
  cursor: pointer;
  font-size: 1.1875rem;
  padding: 0 0.5rem;

  &:hover {
    opacity: 0.65;
  }
}

.name-hoverable {
  &:hover {
    background: $themed-lighter;
  }
}
.name-display {
  display: table;
  width: 100%;
  font-size: 14px;
}

.avatar-name {
  color: $themed-base;
  font-weight: 500;
  max-width: 180px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

</style>
