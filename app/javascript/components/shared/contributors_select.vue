<template>
  <div
    :class="{ 'w-100': !isDiscoveredAssets }"
    :style="discoveredAssetsFieldWidth"
  >
    <div v-if="showAssignees">
      <assignees
        :contributors="valueOptions"
        @remove="removeCompanyUser"
      />
    </div>
    <multi-select
      :id="id"
      ref="multiselect"
      label="name"
      class="w-100"
      :name="name"
      track-by="id"
      allow-empty
      :multiple="allowMultiple"
      :placeholder="currentPlaceholder"
      :options="permissionsOptions ? permissionsOptions : optionsWithAdd"
      :taggable="isScheduledEmail"
      :tag-placeholder="isScheduledEmail ? 'Press enter to add an email' : null"
      :value="valueOptions"
      :show-labels="false"
      :group-select="false"
      :disabled="disabled"
      :group-label="showSelectedAsGroup ? 'groupLabel' : null"
      :group-values="showSelectedAsGroup ? 'groupValues' : null"
      :close-on-select="closeOnSelect"
      :open-direction="insideModal ? 'below' : 'auto'"
      data-tc-multi-user-field
      @remove="removeCompanyUser"
      @select="selectCompanyUser"
      @open="openOptions"
      @search-change="searchQuery"
      @close="selectDropdownClosed"
      @tag="addScheduledEmail"
    >
      <template
        slot="singleLabel"
        slot-scope="props"
      >
        <div class="row flex-nowrap align-items-center mb-2">
          <div
            v-if="value && value.id"
            class="col-auto px-lg-3 px-2"
          >
            <user-avatar
              :username="objName(props.option)"
              :src="userAvatar(props.option)"
              :size="selectSize"
            />
          </div>
          <div
            class="col"
            :class="nameCss"
          >
            <span
              class="avatar-name d-block not-as-small"
              :data-tc-name-selected="objName(props.option)"
            >
              {{ objName(props.option) }}
            </span>
            <span
              v-if="!compact && props.option.type == 'CompanyUser' && !isDiscoveredAssets"
              class="avatar-email d-block true-small flatten-line-height"
            >
              {{ props.option.email }}
            </span>
            <span
              v-if="!compact && props.option.type == 'Group' && !isDiscoveredAssets"
              class="avatar-email d-block true-small flatten-line-height"
            >
              Group
            </span>
          </div>
        </div>
      </template>
      <template
        slot="tag"
        slot-scope="{ option, remove }"
      >
        <span
          v-show="showTags"
          class="multiselect__tag"
        >
          <span class="custom__tag">{{ objName(option) }}</span>
          <i
            aria-hidden="true"
            tabindex="1"
            class="multiselect__tag-icon"
            @click="remove(option)"
          />
        </span>
      </template>

      <template
        slot="option"
        slot-scope="props"
      >
        <div class="row mr-0 align-items-center flex-nowrap">
          <div
            v-if="props.option.id"
            class="col-auto d-inline-block"
          >
            <user-avatar
              :username="objName(props.option)"
              :src="userAvatar(props.option)"
              :size="selectSize"
              class="logo-outline"
            />
          </div>
          <div
            class="col pl-0 d-inline-block"
            :class="{'pl-4': !props.option.id}"
          >
            <span
              class="avatar-name d-block not-as-small"
              :class="{'truncate': truncateText, 'truncate-small': truncateText && excludes.length > 0}"
              :data-tc-selected-object="objName(props.option)"
            >
              {{ objName(props.option) }}
            </span>
            <span
              v-if="props.option.name && props.option.email && props.option.type == 'CompanyUser' && !isDiscoveredAssets"
              class="avatar-email d-block not-as-small"
              :class="{'truncate': truncateText, 'truncate-small': truncateText && excludes.length > 0}"
            >
              {{ props.option.email }}
            </span>
            <span
              v-if="props.option.name && props.option.type && props.option.type == 'Group' && !isDiscoveredAssets"
              class="avatar-email d-block not-as-small"
            >
              Group
            </span>
            <span
              v-if="excludes && excludes.includes(props.option.id)"
              class="not-as-small text-muted"
              :data-tc-remove-selected="objName(props.option)"
            >
              <i
                class="nulodgicon-trash-b remove-link p-1 text-fair"
                data-tc-remove-btn="object"
              />
            </span>
          </div>
        </div>
      </template>
      <template
        v-if="displayShowMore && !isCalendar"
        slot="afterList"
      >
        <div
          class="not-as-small py-3 cursor-pointer text-center"
          style="padding-left: .75rem !important;"
          @click.stop.prevent="showMore"
        >
          <a href="#">+ Show More</a>
        </div>
      </template>
      <template
        v-if="loading"
        slot="caret"
      >
        <clip-loader
          loading
          class="position-absolute"
          color="#000"
          size="1.25rem"
          style="right: 10px; top: 10px;"
        />
      </template>
    </multi-select>
    <Teleport to="body">
      <sweet-modal
        v-if="addNewUser"
        ref="newUserModal"
        v-sweet-esc
        title="Add a new teammate"
        class="w-100"
        blocking
      >
        <template slot="default">
          <company-user-custom-form-viewer
            v-if="showCompanyUserForm"
            render-from-modal
            :new-form="true"
            @new-user="closeNewUserModal"
          />
        </template>
      </sweet-modal>
    </Teleport>
  </div>
</template>

<script>
import http from "common/http";
import _debounce from "lodash/debounce";
import _isEqual from 'lodash/isEqual';
import clipLoader from 'vue-spinner/src/ClipLoader.vue';
import MultiSelect from 'vue-multiselect';
import Teleport from 'vue2-teleport';
import { Avatar as UserAvatar } from 'vue-avatar';
import _get from 'lodash/get';
import _map from 'lodash/map';
import { SweetModal } from 'sweet-modal-vue';
import permissionsHelper from 'mixins/permissions_helper';
import manageStoreOptions from 'mixins/manage_store_options';
import CompanyUserCustomFormViewer from 'components/shared/custom_forms/custom_form_viewer/company_user_custom_form_viewer.vue';
import Assignees from './assignees.vue';

export default {
  components: {
    clipLoader,
    MultiSelect,
    UserAvatar,
    SweetModal,
    CompanyUserCustomFormViewer,
    Assignees,
    Teleport,
  },
  mixins: [permissionsHelper, manageStoreOptions],
  props: {
    insideModal: {
      type: Boolean,
      required: false,
      default: false,
    },
    multiple: {
      type: Boolean,
      required: false,
      default: true,
    },
    id: {
      type: String,
      required: false,
      default: 'company-users',
    },
    placeholder: {
      type: String,
      required: false,
      default: 'Select Teammate',
    },
    value: {
      type: [Object, Array],
      default: null,
      required: false,
    },
    name: {
      type: String,
      default: '',
      required: false,
    },
    compact: {
      type: Boolean,
      required: false,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    excludes: {
      type: Array,
      default: () => [],
    },
    onlyAgents: {
      type: Boolean,
      default: false,
    },
    onlyMembers: {
      type: Boolean,
      default: false,
    },
    truncateText: {
      type: Boolean,
      default: false,
    },
    showSelectedAsGroup: {
      type: Boolean,
      default: false,
    },
    ticket: {
      type: Object,
      default: null,
    },
    isListView: {
      type: Boolean,
      default: false,
    },
    isDiscoveredAssets: {
      type: Boolean,
      default: false,
    },
    addNewUser: {
      type: Boolean,
      default: false,
    },
    allowMultiple: {
      type: Boolean,
      default: false,
    },
    showAssignees: {
      type: Boolean,
      default: false,
    },
    isSlaEmail: {
      type: Boolean,
      default: false,
    },
    groupContributors: {
      type: Array,
      default: () => [],
    },
    closeOnSelect: {
      type: Boolean,
      default: true,
    },
    showTags: {
      type: Boolean,
      default: true,
    },
    isScheduledEmail: {
      type: Boolean,
      default: false,
    },
    permissionsOptions: {
      type: [Object, Array],
      default: null,
      required: false,
    },
    isAccessGroups: {
      type: Boolean,
      default: false,
    },
    isCalendar: {
      type: Boolean,
      default: false,
    },
    isHelpTicketShow: {
      type: Boolean,
      default: false,
    },
    automatedTaskAssignValue: {
      type: Boolean,
      default: false,
    },
    customFormAtAssignValue: {
      type: Boolean,
      default: false,
    },
    field: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      options: [],
      query: null,
      offset: 0,
      disableShowMore: false,
      contributorKey: 'default',
      pageSize: 50,
      loading: false,
      valueOptions: [],
      randomizer: Math.random(),
      showCompanyUserForm: false,
      dropDownPlaceHolder: '',
      excludeSelectedUsers: false,
      agentAssigned: {
        id: "assigned",
        type: "assigned",
        email: "assigned",
        avatarThumbUrl: null,
        name: "Agent Assigned",
      },
      selectedIds: [],
    };
  },
  computed: {
    currentPlaceholder() {
      if (this.loading) {
        return "Loading...";
      }
      if (this.dropDownPlaceHolder) {
        return this.dropDownPlaceHolder;
      }
      return this.placeholder;
    },
    incompleteValues() {
      if (
        !this.value ||
        (typeof this.value === Array && this.value.length === 0) ||
        (typeof this.value === Object && !this.value.id)
      ) {
        return [];
      }
      if (typeof this.value === Array) {
        return this.value.select((v) => !v.name);
      }
      return this.value.name ? [this.value] : [];
    },
    displayShowMore() {
      return !this.disableShowMore;
    },
    nameCss() {
      return `${_get(this, 'value.id') ? "pl-0" : "pl-3"}`;
    },
    selectSize() {
      return this.compact ? 20 : 30;
    },
    filteredOptions() {
      let filterOptions = [];

      if (this.showSelectedAsGroup) {
        filterOptions = [
          {
            groupLabel: "Selected",
            groupValues: this.options.filter(option => this.excludes.includes(option.id)),
          },
          {
            groupLabel: "Unselected",
            groupValues: this.options.filter(option => !this.excludes.includes(option.id)),
          },
        ];
      } else if (this.excludes?.[0]?.id) {
        // Filtered in this manner to handle data issue with permissions contributor selection
        filterOptions = this.options.filter(option => !_map(this.excludes, 'id').includes(option.id));
      } else {
        filterOptions = this.options.filter(option => !this.excludes.includes(option.id));
      }

      if (this.isHelpTicketShow && this.isExternalUser()) {
        filterOptions.find(group => group.groupLabel === "Unselected").groupValues.push({
          id: this.query,
          name: this.query,
        });
      }

      if (this.isListView && filterOptions.length > 0) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.valueOptions = filterOptions[0].groupValues;
      }

      if (this.showSelectedAsGroup) {
        filterOptions = filterOptions.map(group => ({
          ...group,
          groupValues: [...new Map(group.groupValues.map(item => [item.id, item])).values()],
        }));
      }

      return (this.automatedTaskAssignValue || this.customFormAtAssignValue) ? filterOptions.filter(contributor => contributor.active) : filterOptions;
    },
    optionsWithAdd() {
      let uniqueFilteredOptions;
      if (!this.showSelectedAsGroup) {
        uniqueFilteredOptions = [
          ...new Map(this.filteredOptions.map(item => [item.id, item])).values(),
        ];
      } else {
        uniqueFilteredOptions = this.filteredOptions;
      }
      if (!this.addNewUser) {
        return uniqueFilteredOptions;
      }
      const newUser = {
        name: "+ Add a new teammate",
        avatarThumbUrl: "https://cdns.iconmonstr.com/wp-content/assets/preview/2018/240/iconmonstr-plus-circle-thin.png",
        email: null,
        id: 0, // can't be null, otherwise vue-multiselect thinks it's already selected
        type: "CompanyUser",
      };
      return [newUser, ...uniqueFilteredOptions];
    },
    discoveredAssetsFieldWidth() {
      if (this.isDiscoveredAssets) {
        return "width : 14rem";
      }
      return null;
    },
    selectedOptionsUrl() {
        if (this.field?.fieldAttributeType === 'people_list' && this.isHelpTicketShow && this.field?.audience === "guests") {
        return '/contributor_options_with_guests.json';
      }
      return "/contributor_options.json";
    },
  },
  watch: {
    async value() {
      await this.initValueOptions();
    },
  },
  methods: {
    async onWorkspaceChange() {
      await this.initValueOptions();
    },
    selectDropdownClosed() {
      this.dropDownPlaceHolder = '';
      this.query = null;
      this.offset = 0;
      this.loadMore();
      this.$emit("list-open", false);
    },
    clear() {
      this.valueOptions = [];
    },
    openNewUserModal() {
      this.showCompanyUserForm = true;
      this.$refs.newUserModal.open();
    },
    async closeNewUserModal() {
      this.$refs.newUserModal.close();
      this.showCompanyUserForm = false;
      await this.initValueOptions();
    },
    showMore() {
      if (!this.options || this.options.length === 0) {
        this.offset = 0;
      } else {
        this.offset += this.pageSize;
      }
      this.loadMore();
    },
    toggle() {
      if (this.$refs.multiselect) {
        this.$refs.multiselect.toggle();
      }
    },
    openOptions() {
      if (this.placeholder === 'Loading...' || this.loading) {
        this.dropDownPlaceHolder = 'Select People';
        this.loading = false;
      }
      this.$emit("list-open", true);
      this.disableShowMore = true;
      this.options = [];
      this.offset = 0;
      this.setupPusherListeners();
      if (this.shouldFetchStoreOptions() && this.isHelpTicketShow) {
        this.handleStoreOptionsWithGuests();
      } else if (this.contributorOptions?.options[this.contributorKey]) {
        this.handleStoreOptions();
      } else {
        this.loadMore();
      }
    },
    shouldFetchStoreOptions() {
      return (this.isContributorOptionsGuestUrl && this.contributorOptionsWithGuests?.options[this.groupContributorsKey]?.length) ||
      (this.isContributorOptionsUrl && !this.showGroupHeaders && this.contributorOptions?.options[this.groupContributorsKey]?.length) ||
      (this.isContributorOptionsUrl && this.showGroupHeaders && this.contributorOptions?.assignedToFieldOptions?.length);
    },
    objName(obj) {
      if (obj) {
        return obj.name || obj.fullName || obj.email || obj.$groupLabel || this.placeholder;
      }
      return this.placeholder;
    },
    selectCompanyUser(obj) {
      this.dropDownPlaceHolder = '';
      if (obj && obj.id === 0 && !this.isSlaEmail) {
        if (this.insideModal) {
          this.$emit('open-user-modal');
        } else {
          this.openNewUserModal();
        }
        return;
      }

      this.valueOptions = [obj];
      this.$emit('select', obj);
    },
    addScheduledEmail(email) {
      if (this.isScheduledEmail) {
        this.$emit('tag', email);
      }
    },
    removeCompanyUser(obj) {
      this.dropDownPlaceHolder = '';
      const idx = this.valueOptions.indexOf(obj);
      if (idx > -1) {
        this.valueOptions.splice(idx, 1);
      }
      this.$emit('remove', obj);
    },
    async loadMore() {
      const params = { offset: this.offset, limit: this.pageSize };
      if (this.query && this.query.length > 0) {
        params.query = this.query;
      }
      let ids = [];

      if (!this.valueOptions) {
        await this.initValueOptions();
      }

      if (this.valueOptions.length > 0) {
        for (let idx = 0; idx < this.valueOptions.length; idx += 1) {
          ids.push(this.valueOptions[idx].id);
        }
      }
      if (this.excludes?.[0]?.id) {
        ids = ids.concat(this.excludes);
      }
      if (!this.isListView || this.excludeSelectedUsers) {
        params.excludes = ids;
      }
      if (this.isListView) {
        this.excludeSelectedUsers = true;
      }
      if (this.onlyAgents) {
        params.only_agents = this.onlyAgents;
      }
      if (this.onlyMembers) {
        params.only_members = this.onlyMembers;
      }
      if (this.isAccessGroups) {
        params.is_access_groups = this.isAccessGroups;
      }
      if (this.ticket) {
        params.company_id = this.ticket.company.id;
      }
      if (this.groupContributors && this.groupContributors.length) {
        params.group_contributor_ids = _map(this.groupContributors, 'contributorId');
      }
      this.fetchOptions(params);
    },
    fetchValueOptions() {
      const ids = this.valueOptions.filter((v) => !v.name).map((v) => v.id);
      if (!ids.length || !ids[0]) {
        return;
      }

      const params = { includes: ids };

      if (this.ticket) {
        params.company_id = this.ticket.company.id;
      }

      if (this.onlyAgents) {
        params.only_agents = this.onlyAgents;
      }
      if (this.onlyMembers) {
        params.only_members = this.onlyMembers;
      }
      if (this.isAccessGroups) {
        params.is_access_groups = this.isAccessGroups;
      }
      http
        .get(this.selectedOptionsUrl, { params })
        .then((res) => {
          const options = res.data;
          const optionMap = {};
          for (let i = 0; i < options.length; i += 1) {
            optionMap[options[i].id] = options[i];
          }
          for (let i = 0; i < this.valueOptions.length; i += 1) {
            if (!this.valueOptions[i].name) {
              this.valueOptions[i] = optionMap[this.valueOptions[i].id];
            }
          }
          this.valueOptions = [...this.valueOptions];
        })
        .catch(() => {
          this.emitError(
            `Sorry, there was an error loading user/group options. Please try again later.`
          );
        });
    },
    fetchOptions(params) {
      http
        .get(this.selectedOptionsUrl, { params })
        .then((res) => {
          if (this.isHelpTicketShow) {
            this.handleOptionResultsWithGuests(this.offset, res.data);
          } else {
            this.handleOptionResults(this.offset, res.data);
          }
        })
        .catch(() => {
          this.emitError(
            `Sorry, there was an error loading user/group options. Please try again later.`
          );
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleStoreOptionsWithGuests() {
      this.extractSelectedIds();
      if (this.isContributorOptionsGuestUrl) {
        if (!_isEqual(this.selectedIds, this.contributorOptionsWithGuests.selectedIds)) {
          this.updateSelectedIdsAndLoadMore();
          return;
        }
        this.options = this.contributorOptionsWithGuests.options[this.groupContributorsKey];
        this.disableShowMore = this.contributorOptionsWithGuests.disableShowMore[this.groupContributorsKey];
      } else if (this.isContributorOptionsUrl) {
        if (!_isEqual(this.selectedIds, this.contributorOptions.selectedIds)) {
          this.updateSelectedIdsAndLoadMore();
          return;
        }
        this.options = this.showGroupHeaders ? this.contributorOptions.assignedToFieldOptions : this.contributorOptions.options[this.groupContributorsKey];
        this.disableShowMore = this.contributorOptions.disableShowMore[this.groupContributorsKey];
      }
      this.showMoreLoading = false;
      this.offset = this.options.length - 20;
    },
    handleStoreOptions() {
      this.showMoreLoading = false;
      this.options = this.contributorOptions.options[this.contributorKey];
      const hasAssignedAgent = this.options.some(option => option.type === "assigned");
      if (this.isSlaEmail && !hasAssignedAgent) {
        this.options = [this.agentAssigned].concat(this.contributorOptions.options[this.contributorKey]);
      }
      this.disableShowMore = this.contributorOptions.disableShowMore[this.contributorKey];
      this.offset = this.options.length - 50;
    },
    handleOptionResultsWithGuests(offset, data) {
      this.showMoreLoading = false;
      if (offset === 0) {
        this.options = data;
      } else {
        this.options = this.options.concat(data);
      }
      if (data.length < 20) {
        this.disableShowMore = true;
      } else {
        this.disableShowMore = false;
      }
      this.extractSelectedIds();

      if (this.isContributorOptionsGuestUrl) {
        this.setContributorOptionsWithGuests({ options: { [this.groupContributorsKey]: [...this.options] }, disableShowMore: { [this.groupContributorsKey]: this.disableShowMore }, selectedIds: this.selectedIds });
      } else if (this.isContributorOptionsUrl) {
        if (this.showGroupHeaders) {
          this.setContributorOptions({ selectedIds: this.selectedIds, assignedToFieldOptions: [...this.options], disableShowMore: { [this.groupContributorsKey]: this.disableShowMore }, options: {} });
        } else {
          this.setContributorOptions({ selectedIds: this.selectedIds, options: { [this.groupContributorsKey]: [...this.options] }, disableShowMore: { [this.groupContributorsKey]: this.disableShowMore }, assignedToFieldOptions: [] });
        }
      }
    },
    handleOptionResults(offset, data) {
      this.showMoreLoading = false;
      if (offset === 0) {
        this.options = this.valueOptions.concat(data);
      } else {
        this.options = this.options.concat(data);
      }
      if (this.isSlaEmail) {
        this.options = [this.agentAssigned].concat(data);
      }
      if (data.length < this.pageSize) {
        this.disableShowMore = true;
      } else {
        this.disableShowMore = false;
      }
      this.setContributorOptions({ options: { [this.contributorKey]: [...this.options] }, disableShowMore: { [this.contributorKey]: this.disableShowMore }});
    },
    searchQuery(query) {
      if (this.query !== query) {
        this.loading = true;
        this.asyncFind(query);
      }
    },
    // eslint-disable-next-line func-names
    asyncFind: _debounce(function (query) {
      this.query = query;
      this.offset = 0;
      this.loadMore();
    }, 700),
    async initValueOptions() {
      if (this.isHelpTicketShow) {
        await this.$nextTick();
        if (this.$refs.multiselect) {
          this.$refs.multiselect.$el.querySelector('input').focus();
        }
      }
      if (!this.value) {
        this.valueOptions = [];
        return;
      }
      if (this.value instanceof Array) {
        const myOptions = this.value.filter((o) => o.id);
        this.valueOptions = [...myOptions];
      } else if (this.value instanceof Object) {
        if (this.value && this.value.id) {
          const v = { ...this.value };
          this.valueOptions = [v];
        }
      }
      this.fetchValueOptions();
    },
    userAvatar(option) {
      return option.avatar || option.avatarThumbUrl;
    },
    isExternalUser() {
      if (!this.field) return false;
      const REGEX_PATTERN = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return (
        this.field?.fieldAttributeType === "people_list" &&
        this.field?.audience === "guests" &&
        this.query?.indexOf("@") > 0 && REGEX_PATTERN.exec(this.query)
      );
    },
    extractSelectedIds() {
      this.selectedIds = [];
      for (let idx = 0; idx < this.value.length; idx += 1) {
        if (this.value[idx].valueInt) {
          this.selectedIds.push(this.value[idx].valueInt);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
  .multiselect {
    :deep(.multiselect__tags) {
      border-color: $themed-fair;
      border-radius: $border-radius;
    }

    :deep(.multiselect__option--selected) {
      background-color: $themed-lighter;

      &.multiselect__option--highlight {
        background-color: $red-light;

        :deep(.remove-link) {
          color: $themed-lighter;
        }
      }
    }
  }

  .multiselect__tags {
    border-color: $themed-fair;
  }

  .multiselect__tag {
    overflow: inherit !important;
    white-space: unset;
  }

  .avatar-name {
    color: $themed-base;
    font-weight: 500;
    max-width: 14.725rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .multiselect__option--highlight & {
      color: white;
    }
  }

  .avatar-email {
    color: $text-muted;
    max-width: 13.125rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .multiselect__option--highlight & {
      color: white;
    }
  }

  .multiselect {
    :deep(.multiselect__single) {
      margin-bottom: 0px;
    }
  }

  .truncate {
    max-width: 165px;
  }

  .truncate-small {
    max-width: 145px;
  }

  .remove-link {
    bottom: 0.25rem;
    position: absolute;
    right: -0.25rem;
  }
</style>
