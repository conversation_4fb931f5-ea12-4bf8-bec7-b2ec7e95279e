export default {
  data() {
    return {
      months: [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December",
      ],
    };
  },
  computed: {
    tableHeading() {
      const { currentView, currentYear, currentMonth, selectedDate, selectedMonth } = this;

      if (currentView === "Calendar Year" && selectedMonth === null) return `${currentYear}`;
      if (currentView === "Upcoming 12 Months" && selectedMonth === null) return `${this.getMonthName(currentMonth)} ${currentYear}`;
      if ((currentView === "Calendar Year" && selectedMonth !== null) || (currentView === "Month" && !selectedDate)) {
        return `${this.getMonthName(currentMonth)} ${currentYear}`;
      }
      if (currentView === "Month" && selectedDate instanceof Date && !Number.isNaN(selectedDate)) {
        return `${this.getMonthName(currentMonth)} ${this.getOrdinal(selectedDate.getDate())} ${currentYear}`;
      }
      return "";
    },
  },

  methods: {
    getMonthName(monthIndex) {
      return this.months[monthIndex];
    },
    getMonthIndex(monthName) {
      const monthWithoutYear = monthName.replace(/\d+/g, '').trim().toLowerCase();
      return this.months.findIndex(name => name.toLowerCase() === monthWithoutYear);
    },
    parseSelectedMonth(selectedMonth, currentYear) {
      const monthNameToNumber = {
        january: 1,
        february: 2,
        march: 3,
        april: 4,
        may: 5,
        june: 6,
        july: 7,
        august: 8,
        september: 9,
        october: 10,
        november: 11,
        december: 12,
      };

      if (!selectedMonth) return null;

      const lower = selectedMonth.toLowerCase();

      const fullMatch = lower.match(/^([a-z]+)(\d{4})$/);
      if (fullMatch) {
        const month = monthNameToNumber[fullMatch[1]];
        const year = parseInt(fullMatch[2], 10);
        return month ? { month, year } : null;
      }

      if (monthNameToNumber[lower]) {
        return { month: monthNameToNumber[lower], year: currentYear };
      }
      return null;
    },
    checkSortCookies() {
      if (!this.sortPresentInCookie()) return;

      const cookieSortItem = this.getCookieValue("contracts-calendar-sort-item");
      const cookieSortDirection = this.getCookieValue("contracts-calendar-sort-direction");

      if (cookieSortItem && cookieSortDirection) {
        this.activeSort = cookieSortItem;
        this.activeSortDirection = cookieSortDirection;
      } else {
        this.setActiveSort();
        this.setActiveSortDirection();
      }
    },
    setSortCookies() {
      this.setActiveSortInCookie("contracts-calendar", this.activeSort, this.activeSortDirection);
    },
    sortByItem(contract) {
      const contractTermInDays = (term) => {
        const [, value, unit] = term?.match(/(\d+)\s*(days|months)?/i) || [];
        return unit?.toLowerCase() === "months" ? value * 30 : Number(value) || 0;
      };

      return {
        contract_name: contract.name || "",
        end_date: contract.endDate ? new Date(contract.endDate) : new Date(0),
        vendor_name: contract.vendor?.name?.toLowerCase() || "",
        cost: contract.monthlyCost ?? 0,
        contract_term: contractTermInDays(contract.contractTerm),
        contract_alert_recipients: contract.contacts?.map(({ fullName }) => fullName.toLowerCase()).join(", ") || "",
      }[this.activeSort] ?? "";
    },
  },
};
